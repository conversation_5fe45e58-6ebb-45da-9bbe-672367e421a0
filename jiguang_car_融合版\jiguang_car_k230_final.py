# jiguang_car_k230_final.py - K230激光车完整应用层文件(无屏幕版)
# 作者: combo | 版本: 1.0.1 | 描述: 无敌版，满分代码
# 基于K230 CanMV开发环境

import time
import os
import sys
from media.sensor import *
from media.media import *
from machine import FPIOA
from machine import UART
import math

# ========================== 全局变量 ==========================
sensor = None
uart2 = None
fpioa = None

# ========================== 统一配置类 ==========================
class Config:
    # 摄像头配置
    CAMERA_WIDTH = 400  # 优化分辨率提高处理速度
    CAMERA_HEIGHT = 200

    # UART配置
    UART_TX_PIN = 11  # UART2_TXD
    UART_RX_PIN = 12  # UART2_RXD
    UART_BAUDRATE = 115200
    FRAME_HEADER = "$$"
    FRAME_TAIL = "##"

    # 视觉处理参数 - 基于untitled_2优化
    MIN_RECT_THRESHOLD = 5000   # 降低阈值提高检测灵敏度
    MIN_AREA_THRESHOLD = 50     # 最小面积阈值
    BINARY_THRESHOLD_MIN = 14   # 黑色检测范围
    BINARY_THRESHOLD_MAX = 92

    # 矩形验证参数
    MIN_WIDTH = 50              # 最小宽度
    MIN_HEIGHT = 30             # 最小高度
    MIN_MAGNITUDE = 300000      # 最小magnitude值
    MIN_ASPECT_RATIO = 1.0      # 宽高比：宽度必须大于高度

    # 稳定性参数
    DETECTION_HISTORY_SIZE = 5  # 检测历史缓存大小
    MIN_STABLE_FRAMES = 3       # 最少稳定帧数

    # 透视变换参数
    CORRECTED_WIDTH = 200
    CORRECTED_HEIGHT = 150
    CIRCLE_RADIUS = 50
    CIRCLE_NUM_POINTS = 12

    # 控制配置
    MODE_SWITCH_INTERVAL = 10.0  # 模式自动切换间隔(秒)
    TARGET_X = 200  # 目标中心X坐标(调整为新分辨率)
    TARGET_Y = 100  # 目标中心Y坐标

# ========================== UART通信管理器 ==========================
class UARTManager:
    def __init__(self):
        self.uart = None
        self.is_initialized = False

    def init(self):
        global fpioa, uart2
        try:
            # 配置UART引脚
            fpioa = FPIOA()
            fpioa.set_function(Config.UART_TX_PIN, FPIOA.UART2_TXD)
            fpioa.set_function(Config.UART_RX_PIN, FPIOA.UART2_RXD)

            # 初始化UART
            uart2 = UART(UART.UART2, Config.UART_BAUDRATE)
            self.uart = uart2
            self.is_initialized = True
            print("UART初始化成功")
            return True
        except Exception as e:
            print(f"UART初始化失败: {e}")
            return False

    def send(self, data):
        if not self.is_initialized or not self.uart:
            return False
        try:
            frame_data = f"{Config.FRAME_HEADER}{data}{Config.FRAME_TAIL}\n"
            self.uart.write(frame_data)
            return True
        except Exception as e:
            print(f"发送失败: {e}")
            return False

    def printf(self, format_str, *args):
        try:
            output = format_str % args if args else str(format_str)
            print(output)  # 控制台输出
            if self.is_initialized:
                return self.send(output)
            return False
        except Exception as e:
            print(f"printf错误: {e}")
            return False

# ========================== 视觉处理器 ==========================
class VisionProcessor:
    def __init__(self):
        self.detection_history = []  # 检测历史记录
        self.stable_rect = None      # 稳定的矩形
        self.stable_count = 0        # 稳定计数

    def detect_rectangles(self, img):
        """使用K230内置的矩形检测功能 - 增强稳定性"""
        try:
            # 转换为灰度图
            img_gray = img.to_grayscale(copy=True)
            # 二值化处理 - 检测黑色区域
            black_threshold = [(Config.BINARY_THRESHOLD_MIN, Config.BINARY_THRESHOLD_MAX, -27, 19, -33, 34)]
            img_binary = img.binary(black_threshold, invert=False)

            # 查找矩形
            rects = img_binary.find_rects(threshold=Config.MIN_RECT_THRESHOLD,
                                        area_threshold=Config.MIN_AREA_THRESHOLD)

            if rects:
                # 筛选有效矩形
                valid_rects = []
                for rect in rects:
                    if self._is_valid_rectangle(rect, img_binary):
                        valid_rects.append(rect)

                # 选择最佳矩形
                if valid_rects:
                    best_rect = self._select_best_rectangle(valid_rects)
                    return [best_rect] if best_rect else []

            return []
        except Exception as e:
            print(f"矩形检测错误: {e}")
            return []

    def _is_valid_rectangle(self, rect, binary_img):
        """验证矩形是否有效 - 基于untitled_2的多重验证"""
        try:
            # 1. 尺寸验证
            if rect.w() < Config.MIN_WIDTH or rect.h() < Config.MIN_HEIGHT:
                return False

            # 2. 宽高比验证 - 宽度必须大于高度
            if rect.w() <= rect.h():
                return False

            # 3. magnitude验证
            if rect.magnitude() < Config.MIN_MAGNITUDE:
                return False

            # 4. 中心点像素验证 - 检查中心是否为黑色
            center_x = int(rect.w()/2 + rect.x())
            center_y = int(rect.h()/2 + rect.y())

            # 确保中心点在图像范围内
            if (center_x > 0 and center_y > 0 and
                center_x < Config.CAMERA_WIDTH and center_y < Config.CAMERA_HEIGHT):
                # 检查中心点像素是否为黑色(0,0,0)
                pixel_value = binary_img.get_pixel(center_x, center_y)
                if pixel_value != (0, 0, 0):
                    return False
            else:
                return False

            return True
        except Exception as e:
            print(f"矩形验证错误: {e}")
            return False

    def _select_best_rectangle(self, rects):
        """选择最佳矩形 - 基于magnitude和稳定性"""
        if not rects:
            return None

        # 选择magnitude最大的矩形
        best_rect = max(rects, key=lambda r: r.magnitude())

        # 更新检测历史
        self._update_detection_history(best_rect)

        # 检查稳定性
        if self._is_detection_stable():
            self.stable_rect = best_rect
            return best_rect

        # 如果不稳定，返回之前稳定的矩形
        return self.stable_rect if self.stable_rect else best_rect

    def _update_detection_history(self, rect):
        """更新检测历史"""
        center_x, center_y = self.get_rectangle_center(rect)
        if center_x is not None:
            self.detection_history.append((center_x, center_y))

            # 保持历史记录大小
            if len(self.detection_history) > Config.DETECTION_HISTORY_SIZE:
                self.detection_history.pop(0)

    def _is_detection_stable(self):
        """检查检测是否稳定"""
        if len(self.detection_history) < Config.MIN_STABLE_FRAMES:
            return False

        # 计算最近几帧的位置变化
        recent_positions = self.detection_history[-Config.MIN_STABLE_FRAMES:]

        # 计算位置方差
        x_positions = [pos[0] for pos in recent_positions]
        y_positions = [pos[1] for pos in recent_positions]

        x_variance = sum((x - sum(x_positions)/len(x_positions))**2 for x in x_positions) / len(x_positions)
        y_variance = sum((y - sum(y_positions)/len(y_positions))**2 for y in y_positions) / len(y_positions)

        # 如果位置变化小于阈值，认为稳定
        return x_variance < 100 and y_variance < 100

    def get_rectangle_center(self, rect):
        """获取矩形中心点"""
        try:
            corners = rect.corners()
            if len(corners) >= 4:
                center_x = sum([corner[0] for corner in corners]) / 4
                center_y = sum([corner[1] for corner in corners]) / 4
                return int(center_x), int(center_y)
            return None, None
        except Exception as e:
            print(f"获取中心点错误: {e}")
            return None, None

    def generate_circle_points(self, center_x, center_y, radius, num_points):
        """生成圆形轨迹点"""
        circle_points = []
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            x = int(center_x + radius * math.cos(angle))
            y = int(center_y + radius * math.sin(angle))
            circle_points.append((x, y))
        return circle_points

# ========================== 主应用类 ==========================
class JiguangCarApp:
    def __init__(self):
        self.uart_manager = UARTManager()
        self.vision_processor = VisionProcessor()
        self.current_mode = "center"  # center 或 circle
        self.last_mode_switch = time.time()
        self.frame_count = 0

    def init_devices(self):
        """初始化所有设备"""
        global sensor
        try:
            print("K230激光车程序启动...")

            # 初始化摄像头
            sensor = Sensor(width=Config.CAMERA_WIDTH, height=Config.CAMERA_HEIGHT)
            sensor.reset()
            sensor.set_framesize(width=Config.CAMERA_WIDTH, height=Config.CAMERA_HEIGHT)
            sensor.set_pixformat(Sensor.RGB565)

            # 初始化媒体管理器
            MediaManager.init()
            sensor.run()

            # 初始化UART
            if not self.uart_manager.init():
                print("UART初始化失败")
                return False

            print("所有设备初始化完成")
            return True
        except Exception as e:
            print(f"设备初始化失败: {e}")
            return False

    def process_frame(self, img):
        """处理单帧图像 - 优化版本"""
        try:
            # 检测矩形
            rects = self.vision_processor.detect_rectangles(img)

            if rects and len(rects) > 0:
                # 获取最佳矩形
                best_rect = rects[0]  # detect_rectangles已经返回最佳矩形

                # 在图像上绘制矩形
                corners = best_rect.corners()
                if len(corners) >= 4:
                    # 绘制矩形边框
                    for i in range(4):
                        start = corners[i]
                        end = corners[(i + 1) % 4]
                        img.draw_line(start[0], start[1], end[0], end[1],
                                    color=(0, 255, 0), thickness=3)

                    # 绘制角点
                    for corner in corners:
                        img.draw_circle(corner[0], corner[1], 5, color=(0, 255, 0), thickness=-1)

                # 获取中心点
                center_x, center_y = self.vision_processor.get_rectangle_center(best_rect)

                if center_x is not None and center_y is not None:
                    # 绘制中心点和十字标记
                    img.draw_circle(center_x, center_y, 8, color=(255, 0, 0), thickness=-1)
                    img.draw_cross(center_x, center_y, color=(255, 255, 0), size=15, thickness=3)

                    # 显示坐标信息
                    coord_text = f"({center_x},{center_y})"
                    img.draw_string_advanced(center_x + 10, center_y - 10, 20,
                                           coord_text, color=(255, 255, 255))

                    # 根据模式发送数据
                    if self.current_mode == "center":
                        self.uart_manager.printf(f"R,{center_x},{center_y}")
                    elif self.current_mode == "circle":
                        # 生成圆形轨迹点
                        circle_points = self.vision_processor.generate_circle_points(
                            center_x, center_y, Config.CIRCLE_RADIUS, Config.CIRCLE_NUM_POINTS
                        )

                        # 绘制圆形轨迹
                        for i, point in enumerate(circle_points):
                            img.draw_circle(point[0], point[1], 3, color=(0, 0, 255), thickness=-1)
                            # 绘制点序号
                            if i % 3 == 0:  # 每3个点显示一个序号
                                img.draw_string_advanced(point[0] + 5, point[1] - 5, 15,
                                                       str(i), color=(255, 255, 0))

                        # 发送圆形数据
                        circle_data = f"C,{len(circle_points)}"
                        for point in circle_points:
                            circle_data += f",{point[0]},{point[1]}"
                        self.uart_manager.printf(circle_data)

                    # 显示矩形信息
                    rect_info = f"W:{best_rect.w()} H:{best_rect.h()} M:{best_rect.magnitude():.0f}"
                    img.draw_string_advanced(10, 130, 20, rect_info, color=(0, 255, 255))

            else:
                # 没有检测到矩形
                if self.current_mode == "center":
                    self.uart_manager.printf("R,0,0")

                # 显示无目标提示
                img.draw_string_advanced(10, 130, 25, "NO TARGET", color=(255, 0, 0))

            # 绘制目标点十字标记
            img.draw_line(Config.TARGET_X - 15, Config.TARGET_Y,
                         Config.TARGET_X + 15, Config.TARGET_Y,
                         color=(255, 0, 255), thickness=3)
            img.draw_line(Config.TARGET_X, Config.TARGET_Y - 15,
                         Config.TARGET_X, Config.TARGET_Y + 15,
                         color=(255, 0, 255), thickness=3)

            # 显示目标坐标
            target_text = f"Target({Config.TARGET_X},{Config.TARGET_Y})"
            img.draw_string_advanced(Config.TARGET_X + 20, Config.TARGET_Y - 10, 18,
                                   target_text, color=(255, 0, 255))

            return img
        except Exception as e:
            print(f"帧处理错误: {e}")
            return img

    def auto_switch_mode(self):
        """自动切换模式"""
        current_time = time.time()
        if current_time - self.last_mode_switch > Config.MODE_SWITCH_INTERVAL:
            self.current_mode = "circle" if self.current_mode == "center" else "center"
            self.last_mode_switch = current_time
            print(f"模式切换为: {self.current_mode}")

    def run(self):
        """主运行循环"""
        if not self.init_devices():
            return

        clock = time.clock()

        try:
            while True:
                clock.tick()
                os.exitpoint()

                # 获取图像
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                if img is None:
                    continue

                self.frame_count += 1

                # 自动切换模式
                self.auto_switch_mode()

                # 处理图像
                processed_img = self.process_frame(img)

                # 绘制状态信息
                processed_img.draw_string_advanced(10, 10, 25,
                    f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
                processed_img.draw_string_advanced(10, 35, 25,
                    f"Mode: {self.current_mode.upper()}", color=(0, 255, 255))
                processed_img.draw_string_advanced(10, 60, 20,
                    f"Frame: {self.frame_count}", color=(255, 255, 0))
                processed_img.draw_string_advanced(10, 85, 20,
                    f"Stable: {self.vision_processor.stable_count}", color=(0, 255, 0))

                # 压缩图像用于IDE显示和传输
                self._compress_and_display(processed_img)

        except KeyboardInterrupt:
            print("用户停止程序")
        except Exception as e:
            print(f"运行错误: {e}")
        finally:
            self.cleanup()

    def _compress_and_display(self, img):
        """压缩图像用于显示和传输"""
        try:
            # 方法1: 使用midpoint_pool进行下采样压缩
            compressed_img = img.copy()
            compressed_img.midpoint_pool(2, 2)  # 2x2下采样

            # 为IDE显示进行压缩
            compressed_img.compressed_for_ide()

            return compressed_img
        except Exception as e:
            print(f"图像压缩错误: {e}")
            # 如果压缩失败，直接使用原图
            img.compressed_for_ide()
            return img

    def cleanup(self):
        """清理资源"""
        global sensor, uart2
        try:
            if sensor:
                sensor.stop()
            if uart2:
                uart2.deinit()
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
            print("资源清理完成")
        except Exception as e:
            print(f"清理错误: {e}")

# ========================== 主程序入口 ==========================
if __name__ == "__main__":
    app = JiguangCarApp()
    app.run()
