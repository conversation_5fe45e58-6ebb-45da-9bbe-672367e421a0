# jiguang_car_k230_final.py - K230激光车完整应用层文件(无屏幕版)
# 作者: combo | 版本: 1.0.1 | 描述: 无敌版，满分代码
# 基于K230 CanMV开发环境

import time
import os
import sys
from media.sensor import *
from media.media import *
from machine import FPIOA
from machine import UART
import math

# ========================== 全局变量 ==========================
sensor = None
uart2 = None
fpioa = None

# ========================== 统一配置类 ==========================
class Config:
    # 摄像头配置
    CAMERA_WIDTH = 640
    CAMERA_HEIGHT = 640

    # UART配置
    UART_TX_PIN = 11  # UART2_TXD
    UART_RX_PIN = 12  # UART2_RXD
    UART_BAUDRATE = 115200
    FRAME_HEADER = "$$"
    FRAME_TAIL = "##"

    # 视觉处理参数
    MIN_RECT_THRESHOLD = 10000  # 矩形检测阈值
    BINARY_THRESHOLD_MIN = 52   # 二值化最小值
    BINARY_THRESHOLD_MAX = 181 # 二值化最大值

    # 透视变换参数
    CORRECTED_WIDTH = 200
    CORRECTED_HEIGHT = 150
    CIRCLE_RADIUS = 50
    CIRCLE_NUM_POINTS = 12

    # 控制配置
    MODE_SWITCH_INTERVAL = 10.0  # 模式自动切换间隔(秒)
    TARGET_X = 320  # 目标中心X坐标
    TARGET_Y = 320  # 目标中心Y坐标

# ========================== UART通信管理器 ==========================
class UARTManager:
    def __init__(self):
        self.uart = None
        self.is_initialized = False

    def init(self):
        global fpioa, uart2
        try:
            # 配置UART引脚
            fpioa = FPIOA()
            fpioa.set_function(Config.UART_TX_PIN, FPIOA.UART2_TXD)
            fpioa.set_function(Config.UART_RX_PIN, FPIOA.UART2_RXD)

            # 初始化UART
            uart2 = UART(UART.UART2, Config.UART_BAUDRATE)
            self.uart = uart2
            self.is_initialized = True
            print("UART初始化成功")
            return True
        except Exception as e:
            print(f"UART初始化失败: {e}")
            return False

    def send(self, data):
        if not self.is_initialized or not self.uart:
            return False
        try:
            frame_data = f"{Config.FRAME_HEADER}{data}{Config.FRAME_TAIL}\n"
            self.uart.write(frame_data)
            return True
        except Exception as e:
            print(f"发送失败: {e}")
            return False

    def printf(self, format_str, *args):
        try:
            output = format_str % args if args else str(format_str)
            print(output)  # 控制台输出
            if self.is_initialized:
                return self.send(output)
            return False
        except Exception as e:
            print(f"printf错误: {e}")
            return False

# ========================== 视觉处理器 ==========================
class VisionProcessor:
    def __init__(self):
        pass

    def detect_rectangles(self, img):
        """使用K230内置的矩形检测功能"""
        try:
            # 转换为灰度图
            img_gray = img.to_grayscale(copy=True)
            # 二值化处理
            img_binary = img_gray.binary([(Config.BINARY_THRESHOLD_MIN, Config.BINARY_THRESHOLD_MAX)])
            # 查找矩形
            rects = img_binary.find_rects(threshold=Config.MIN_RECT_THRESHOLD)

            if rects:
                return rects
            return []
        except Exception as e:
            print(f"矩形检测错误: {e}")
            return []

    def get_rectangle_center(self, rect):
        """获取矩形中心点"""
        try:
            corners = rect.corners()
            if len(corners) >= 4:
                center_x = sum([corner[0] for corner in corners]) / 4
                center_y = sum([corner[1] for corner in corners]) / 4
                return int(center_x), int(center_y)
            return None, None
        except Exception as e:
            print(f"获取中心点错误: {e}")
            return None, None

    def generate_circle_points(self, center_x, center_y, radius, num_points):
        """生成圆形轨迹点"""
        circle_points = []
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            x = int(center_x + radius * math.cos(angle))
            y = int(center_y + radius * math.sin(angle))
            circle_points.append((x, y))
        return circle_points

# ========================== 主应用类 ==========================
class JiguangCarApp:
    def __init__(self):
        self.uart_manager = UARTManager()
        self.vision_processor = VisionProcessor()
        self.current_mode = "center"  # center 或 circle
        self.last_mode_switch = time.time()
        self.frame_count = 0

    def init_devices(self):
        """初始化所有设备"""
        global sensor
        try:
            print("K230激光车程序启动...")

            # 初始化摄像头
            sensor = Sensor(width=Config.CAMERA_WIDTH, height=Config.CAMERA_HEIGHT)
            sensor.reset()
            sensor.set_framesize(width=Config.CAMERA_WIDTH, height=Config.CAMERA_HEIGHT)
            sensor.set_pixformat(Sensor.RGB565)

            # 初始化媒体管理器
            MediaManager.init()
            sensor.run()

            # 初始化UART
            if not self.uart_manager.init():
                print("UART初始化失败")
                return False

            print("所有设备初始化完成")
            return True
        except Exception as e:
            print(f"设备初始化失败: {e}")
            return False

    def process_frame(self, img):
        """处理单帧图像"""
        try:
            # 检测矩形
            rects = self.vision_processor.detect_rectangles(img)

            if rects:
                # 选择最大的矩形
                largest_rect = max(rects, key=lambda r: r.w() * r.h())

                # 在图像上绘制矩形
                corners = largest_rect.corners()
                if len(corners) >= 4:
                    for i in range(4):
                        start = corners[i]
                        end = corners[(i + 1) % 4]
                        img.draw_line(start[0], start[1], end[0], end[1],
                                    color=(0, 255, 0), thickness=3)

                # 获取中心点
                center_x, center_y = self.vision_processor.get_rectangle_center(largest_rect)

                if center_x is not None and center_y is not None:
                    # 绘制中心点
                    img.draw_circle(center_x, center_y, 5, color=(255, 0, 0), thickness=-1)

                    # 根据模式发送数据
                    if self.current_mode == "center":
                        self.uart_manager.printf(f"R,{center_x},{center_y}")
                    elif self.current_mode == "circle":
                        # 生成圆形轨迹点
                        circle_points = self.vision_processor.generate_circle_points(
                            center_x, center_y, Config.CIRCLE_RADIUS, Config.CIRCLE_NUM_POINTS
                        )

                        # 绘制圆形轨迹
                        for point in circle_points:
                            img.draw_circle(point[0], point[1], 2, color=(0, 0, 255), thickness=-1)

                        # 发送圆形数据
                        circle_data = f"C,{len(circle_points)}"
                        for point in circle_points:
                            circle_data += f",{point[0]},{point[1]}"
                        self.uart_manager.printf(circle_data)
            else:
                # 没有检测到矩形
                if self.current_mode == "center":
                    self.uart_manager.printf("R,0,0")

            # 绘制目标点
            img.draw_line(Config.TARGET_X - 10, Config.TARGET_Y,
                         Config.TARGET_X + 10, Config.TARGET_Y,
                         color=(255, 0, 255), thickness=2)
            img.draw_line(Config.TARGET_X, Config.TARGET_Y - 10,
                         Config.TARGET_X, Config.TARGET_Y + 10,
                         color=(255, 0, 255), thickness=2)

            return img
        except Exception as e:
            print(f"帧处理错误: {e}")
            return img

    def auto_switch_mode(self):
        """自动切换模式"""
        current_time = time.time()
        if current_time - self.last_mode_switch > Config.MODE_SWITCH_INTERVAL:
            self.current_mode = "circle" if self.current_mode == "center" else "center"
            self.last_mode_switch = current_time
            print(f"模式切换为: {self.current_mode}")

    def run(self):
        """主运行循环"""
        if not self.init_devices():
            return

        clock = time.clock()

        try:
            while True:
                clock.tick()
                os.exitpoint()

                # 获取图像
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                if img is None:
                    continue

                self.frame_count += 1

                # 自动切换模式
                self.auto_switch_mode()

                # 处理图像
                processed_img = self.process_frame(img)

                # 绘制状态信息
                processed_img.draw_string_advanced(10, 10, 30,
                    f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
                processed_img.draw_string_advanced(10, 50, 30,
                    f"Mode: {self.current_mode.upper()}", color=(0, 255, 255))
                processed_img.draw_string_advanced(10, 90, 30,
                    f"Frame: {self.frame_count}", color=(255, 255, 0))

                # 压缩图像用于IDE显示
                processed_img.compressed_for_ide()

        except KeyboardInterrupt:
            print("用户停止程序")
        except Exception as e:
            print(f"运行错误: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        global sensor, uart2
        try:
            if sensor:
                sensor.stop()
            if uart2:
                uart2.deinit()
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
            print("资源清理完成")
        except Exception as e:
            print(f"清理错误: {e}")

# ========================== 主程序入口 ==========================
if __name__ == "__main__":
    app = JiguangCarApp()
    app.run()
