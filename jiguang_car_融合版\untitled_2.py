# 立创·庐山派-K230-CanMV开发板资料与相关扩展板软硬件资料官网全部开源
# 开发板官网：www.lckfb.com
# 技术支持常驻论坛，任何技术问题欢迎随时交流学习
# 立创论坛：www.jlc-bbs.com/lckfb
# 关注bilibili账号：【立创开发板】，掌握我们的最新动态！
# 不靠卖板赚钱，以培养中国工程师为己任

import time, os, sys
import ustruct
from media.sensor import *
from media.display import *
from media.media import *
from machine import PWM
from machine import UART
from ulab import numpy as np
from machine import FPIOA
from machine import Pin
import time
fpioa = FPIOA()
fpioa.set_function(34,FPIOA.GPIO34)
fpioa.set_function(33,FPIOA.GPIO33)
# 将排针处有PWM功能的引脚配置为PWM功能
fpioa.set_function(47, FPIOA.PWM3)
fpioa.set_function(46, FPIOA.PWM2)
#fpioa.set_function(61, FPIOA.PWM1)
dir_y= Pin(34, Pin.OUT, pull=Pin.PULL_NONE, drive=7)  # 红灯
dir_x = Pin(33, Pin.OUT, pull=Pin.PULL_NONE, drive=7)  # 绿灯

pwm_y = PWM(3, 100, duty=50, enable=False)
pwm_x = PWM(2, 100, duty=50, enable=False)
class PID:
    """
    离散位置式 PID 控制器
    u(k) = Kp * e(k) + Ki * Σe(k) + Kd * [e(k)-e(k-1)]
    """

    def __init__(self, Kp=1.0, Ki=0.0, Kd=0.0,
                 set_point=0.0, sample_time=0.01,
                 out_min=None, out_max=None):
        """
        参数
        ----
        Kp, Ki, Kd : PID 系数
        set_point  : 目标值
        sample_time: 控制周期(s)
        out_min/out_max: 输出限幅
        """
        self.Kp = Kp
        self.Ki = Ki
        self.Kd = Kd
        self.set_point = set_point
        self.sample_time = sample_time
        self.out_min = out_min
        self.out_max = out_max

        # 内部状态
        self._prev_error = 0.0
        self._integral = 0.0
        self._last_time = None

    def update(self, feedback_value):
        """
        计算一次 PID 输出
        :param feedback_value: 当前测量值
        :return: 控制量
        """

        error = self.set_point - feedback_value

        # 比例
        p_out = self.Kp * error

        # 积分
        self._integral += error
        i_out = self.Ki * self._integral

        # 微分
        derivative = (error - self._prev_error)
        d_out = self.Kd * derivative

        # 总输出
        output = p_out + i_out + d_out

        # 限幅
        if self.out_min is not None or self.out_max is not None:
            output = max(self.out_min or float('-inf'),
                         min(self.out_max or float('inf'), output))

        # 更新状态
        self._prev_error = error
        self._last_output = output
        return output

    def reset(self):
        """复位积分和微分项"""
        self._integral = 0.0
        self._prev_error = 0.0
        self._last_time = None
def set_step(speed_x,speed_y):
    if speed_x>0:
        pwm_x.freq(speed_x)
        dir_x.high()
        pwm_x.enable(True)
    elif speed_x<0:
        pwm_x.freq(-speed_x)
        dir_x.low()
        pwm_x.enable(True)
    else:
        pwm_x.enable(False)
    if speed_y>0:
        pwm_y.freq(speed_y)
        dir_y.low()
        pwm_y.enable(True)
    elif speed_y<0:
        pwm_y.freq(-speed_y)
        dir_y.high()
        pwm_y.enable(True)
    else:
        pwm_y.enable(False)
    print("step:",speed_x,speed_y)

# 将指定引脚配置为 UART 功能
fpioa.set_function(11, FPIOA.UART2_TXD)
fpioa.set_function(12, FPIOA.UART2_RXD)

picture_width = 400
picture_height = 200
aim_x=205
aim_y=96
clock = time.clock()
sensor_id = 2
sensor = None

black=[(14, 92, -27, 19, -33, 34)]
# 显示模式选择：可以是 "VIRT"、"LCD" 或 "HDMI"
DISPLAY_MODE = "LCD"

# 根据模式设置显示宽高
if DISPLAY_MODE == "VIRT":
    # 虚拟显示器模式
    DISPLAY_WIDTH = ALIGN_UP(720, 16)
    DISPLAY_HEIGHT = 480
elif DISPLAY_MODE == "LCD":
    # 3.1寸屏幕模式
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480
elif DISPLAY_MODE == "HDMI":
    # HDMI扩展板模式
    DISPLAY_WIDTH = 1920
    DISPLAY_HEIGHT = 1080
else:
    raise ValueError("未知的 DISPLAY_MODE，请选择 'VIRT', 'LCD' 或 'HDMI'")

try:
    # 构造一个具有默认配置的摄像头对象
    sensor = Sensor(id=sensor_id)
    # 重置摄像头sensor
    sensor.reset()
    # 无需进行镜像翻转
    # 设置水平镜像
    # sensor.set_hmirror(False)
    # 设置垂直翻转
    sensor.set_vflip(True)
    uart = UART(UART.UART2, baudrate=115200, bits=UART.EIGHTBITS, parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)
    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_1)
    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)

    # 设置通道0的输出像素格式为RGB565
    sensor.set_pixformat(Sensor.GRAYSCALE, chn=CAM_CHN_ID_1)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)

    # 根据模式初始化显示器
    if DISPLAY_MODE == "VIRT":
        Display.init(Display.VIRT, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, fps=60)
    elif DISPLAY_MODE == "LCD":
        Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
    elif DISPLAY_MODE == "HDMI":
        Display.init(Display.LT9611, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)

    # 初始化媒体管理器
    MediaManager.init()
    # 启动传感器
    sensor.run()

    while True:
        os.exitpoint()
        clock.tick()
        # 捕获通道0的图像
        pid_x = PID(Kp=100, Ki=0, Kd=0.1,
                      set_point=aim_x,
                      out_min=-8000, out_max=8000)
        pid_y = PID(Kp=50, Ki=0, Kd=50,
                                set_point=aim_y,
                                out_min=-8000, out_max=8000)
        img_rgb = sensor.snapshot(chn=CAM_CHN_ID_0)
        img_grey = sensor.snapshot(chn=CAM_CHN_ID_0)
        binary_img=img_rgb.copy().binary(black,invert=False)

        img_corr=img_rgb.copy()
        # 查找线段并绘制
        #binary_img=img.image()
        #binary_img.dilate(2)
        #set_step(8000,10)
        rects = binary_img.find_rects(threshold=5000,area_threshold =50)
        count = 0  # 初始化线段计数器
        if rects:
            max_rect=rects[0]
            for rect in rects:
                for p in rect.corners():
                    img_rgb.draw_circle(p[0], p[1], 5, color=(0, 255, 0))
                if rect.magnitude()>max_rect.magnitude() and rect.w()>rect.h() and rect.w()>50 and rect.h()>30 and rect.magnitude()>300000:
                    if int(rect.w()/2+rect.x())>0 and int(rect.h()/2+rect.y())>0 and int(rect.w()/2+rect.x())<picture_width and int(rect.h()/2+rect.y())<picture_height:
                        if binary_img.get_pixel(int(rect.w()/2+rect.x()),int(rect.h()/2+rect.y()))==(0,0,0):
                            max_rect=rect
            #img_corr.rotation_corr(corners=max_rect.corners())
             # 若想获取更详细的四个顶点，可使用 rect.corners()，该函数会返回一个有四个元祖的列表，每个元组代表矩形的四个顶点，从左上角开始，按照顺时针排序。
            #data=bytearray([0x55,0x53,max_rect.corners()[0][0],max_rect.corners()[0][1],max_rect.corners()[1][0],max_rect.corners()[1][1],max_rect.corners()[2][0],max_rect.corners()[2][1],max_rect.corners()[3][0],max_rect.corners()[3][1],0x0d,0x0a])
            if binary_img.get_pixel(int(max_rect.w()/2+max_rect.x()),int(max_rect.h()/2+max_rect.y()))==(0,0,0) and max_rect.w()>max_rect.h() and max_rect.w()>50 and max_rect.h()>30 and rect.magnitude()>300000 :
                data =ustruct.pack('BBffffffffBB',85,81,max_rect.corners()[0][0],max_rect.corners()[0][1],max_rect.corners()[1][0],max_rect.corners()[1][1],max_rect.corners()[2][0],max_rect.corners()[2][1],max_rect.corners()[3][0],max_rect.corners()[3][1],13,10)
                #uart.write(data)
                print(max_rect)


                p_x=max_rect.corners()[0][0]+max_rect.corners()[1][0]+max_rect.corners()[2][0]+max_rect.corners()[3][0]
                p_y=max_rect.corners()[0][1]+max_rect.corners()[1][1]+max_rect.corners()[2][1]+max_rect.corners()[3][1]
                p_x=(p_x/4)
                p_y=(p_y/4)
                control_x = pid_x.update(p_x)
                control_y = pid_y.update(p_y)
                set_step(control_x,control_y)
                print("pid:",p_x,p_y,control_x,control_y)
                #img_corr.rotation_corr(corners=max_rect.corners())
                img_rgb.draw_cross(int(p_x),int(p_y), max_rect, color=(255, 255, 0), size=10, thickness=2)
            else :
                set_step(0,0)
        else :
            set_step(0,0)
        # 显示捕获的图像，中心对齐，居中显示

        Display.show_image(img_rgb, 0,0,layer=Display.LAYER_OSD1)
        Display.show_image(img_corr, 400,0,layer=Display.LAYER_OSD0)
        received_data = uart.read()
        if received_data:
            received_message = received_data
            print("Received:", received_message)
        print("fps = ",clock.fps())
except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    # 停止传感器运行
    if isinstance(sensor, Sensor):
        sensor.stop()
    # 反初始化显示模块
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(10)
    # 释放媒体缓冲区
    MediaManager.deinit()
