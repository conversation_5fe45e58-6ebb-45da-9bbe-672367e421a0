# jiguang_car_k230.py - K230激光车完整应用层文件
# 作者: combo | 版本: 1.0.1 | 描述: 无敌版，满分代码

from maix import image, display, app, time, camera, touchscreen, uart
import cv2
import numpy as np
import math
import gc
import threading
import re

# ========================== 统一配置类 ==========================
class Config:
    # UART配置
    UART_DEVICE = "/dev/ttyS0"
    UART_BAUDRATE = 115200
    FRAME_HEADER = "$$"
    FRAME_TAIL = "##"
    MAX_BUFFER_SIZE = 1024
    
    # 摄像头配置
    CAMERA_WIDTH = 320
    CAMERA_HEIGHT = 240
    
    # 视觉处理参数
    MIN_CONTOUR_AREA = 500
    MAX_CONTOUR_AREA = 70000
    TARGET_SIDES = 4
    BINARY_THRESHOLD = 66
    MIN_ASPECT_RATIO = 0.6
    MAX_ASPECT_RATIO = 1.7
    
    # 透视变换参数
    CORRECTED_WIDTH = 200
    CORRECTED_HEIGHT = 150
    CIRCLE_RADIUS = 50
    CIRCLE_NUM_POINTS = 12
    
    # 触摸控制配置
    TOUCH_DEBOUNCE = 0.3
    VIRTUAL_BUTTONS = [
        [20, 180, 45, 20, "Center", "center"],
        [115, 180, 50, 20, "Circle", "circle"],
        [180, 180, 25, 20, "T-", "thresh_down"],
        [210, 180, 25, 20, "T+", "thresh_up"]
    ]
    TOUCH_AREAS = [
        [70, 265, 100, 40], [230, 265, 90, 40],
        [330, 265, 50, 40], [390, 265, 50, 40]
    ]
    
    # 显示配置
    TARGET_X = 150
    TARGET_Y = 95
    CROSS_SIZE = 5

# ========================== UART通信管理器 ==========================
class UARTManager:
    def __init__(self):
        self.serial = None
        self.rx_buf = ""
        self.is_initialized = False
        self._lock = threading.Lock()
    
    def init(self):
        try:
            self.serial = uart.UART(Config.UART_DEVICE, Config.UART_BAUDRATE)
            self.serial.set_received_callback(self._on_received)
            self.is_initialized = True
            print("UART初始化成功")
            return True
        except Exception as e:
            print(f"UART初始化失败: {e}")
            return False
    
    def _on_received(self, serial_obj, data):
        try:
            decoded = data.decode('utf-8', errors='replace') if isinstance(data, bytes) else str(data)
            with self._lock:
                self.rx_buf = decoded.strip()
        except Exception as e:
            print(f"接收错误: {e}")
    
    def send(self, data):
        if not self.is_initialized:
            return False
        try:
            frame_data = f"{Config.FRAME_HEADER}{data}{Config.FRAME_TAIL}\r\n"
            result = self.serial.write_str(frame_data)
            return result is not None and result >= 0
        except Exception as e:
            print(f"发送失败: {e}")
            return False
    
    def printf(self, format_str, *args):
        try:
            output = format_str % args if args else str(format_str)
            print(output)
            if self.is_initialized:
                time.sleep_ms(5)
                return self.send(output)
            return False
        except Exception as e:
            print(f"printf错误: {e}")
            return False

# ========================== 视觉处理器 ==========================
class VisionProcessor:
    def __init__(self):
        self.kernel = np.ones((3, 3), np.uint8)
    
    def detect_rectangles(self, img, threshold):
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if not (Config.MIN_CONTOUR_AREA < area < Config.MAX_CONTOUR_AREA):
                continue
            
            epsilon = 0.03 * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            if len(approx) != Config.TARGET_SIDES:
                continue
            
            x, y, w, h = cv2.boundingRect(approx)
            if h == 0:
                continue
            aspect_ratio = w / h
            if not (Config.MIN_ASPECT_RATIO <= aspect_ratio <= Config.MAX_ASPECT_RATIO):
                continue
            
            if self._is_regular_rectangle(approx):
                quads.append((approx, area))
        
        return [max(quads, key=lambda x: x[1])] if quads else []
    
    def _is_regular_rectangle(self, approx):
        if not cv2.isContourConvex(approx):
            return False
        
        pts = approx.reshape(4, 2).astype(np.float32)
        edge_lengths = [
            math.hypot(pts[1][0]-pts[0][0], pts[1][1]-pts[0][1]),
            math.hypot(pts[2][0]-pts[1][0], pts[2][1]-pts[1][1]),
            math.hypot(pts[3][0]-pts[2][0], pts[3][1]-pts[2][1]),
            math.hypot(pts[0][0]-pts[3][0], pts[0][1]-pts[3][1])
        ]
        
        if not (0.8 <= edge_lengths[0]/edge_lengths[2] <= 1.2 and 
                0.8 <= edge_lengths[1]/edge_lengths[3] <= 1.2):
            return False
        
        angles = []
        for i in range(4):
            p_prev = pts[i]
            p_curr = pts[(i+1)%4]
            p_next = pts[(i+2)%4]
            v1 = [p_curr[0]-p_prev[0], p_curr[1]-p_prev[1]]
            v2 = [p_next[0]-p_curr[0], p_next[1]-p_curr[1]]
            dot = v1[0]*v2[0] + v1[1]*v2[1]
            det = v1[0]*v2[1] - v1[1]*v2[0]
            angle = abs(math.degrees(math.atan2(det, dot)))
            angles.append(angle)
        
        return all(85 <= angle <= 95 for angle in angles)
    
    def perspective_transform(self, pts):
        s = pts.sum(axis=1)
        tl = pts[np.argmin(s)]
        br = pts[np.argmax(s)]
        diff = np.diff(pts, axis=1)
        tr = pts[np.argmin(diff)]
        bl = pts[np.argmax(diff)]
        src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
        
        dst_pts = np.array([
            [0, 0], [Config.CORRECTED_WIDTH-1, 0],
            [Config.CORRECTED_WIDTH-1, Config.CORRECTED_HEIGHT-1], 
            [0, Config.CORRECTED_HEIGHT-1]
        ], dtype=np.float32)
        
        M = cv2.getPerspectiveTransform(src_pts, dst_pts)
        ret, M_inv = cv2.invert(M)
        return M, M_inv if ret else None, src_pts
    
    def generate_circle_points(self, center, radius, num_points):
        circle_points = []
        cx, cy = center
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            x = int(cx + radius * math.cos(angle))
            y = int(cy + radius * math.sin(angle))
            circle_points.append((x, y))
        return circle_points

# ========================== 用户界面管理器 ==========================
class UIManager:
    def __init__(self):
        self.last_touch_time = 0
    
    def check_touch(self, touch_x, touch_y):
        current_time = time.time()
        if current_time - self.last_touch_time < Config.TOUCH_DEBOUNCE:
            return None
        
        for i, area in enumerate(Config.TOUCH_AREAS):
            area_x, area_y, area_w, area_h = area
            if area_x <= touch_x <= area_x + area_w and area_y <= touch_y <= area_y + area_h:
                self.last_touch_time = current_time
                return Config.VIRTUAL_BUTTONS[i][5]
        return None
    
    def draw_buttons(self, img, current_mode, threshold):
        for button in Config.VIRTUAL_BUTTONS:
            x, y, w, h, text, action = button
            if (action == "center" and current_mode == "center") or (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)
                thickness = 2
            else:
                color = (255, 255, 255)
                thickness = 2
            cv2.rectangle(img, (x, y), (x + w, y + h), color, thickness)
            text_x = x + (w - len(text) * 4) // 2
            text_y = y + (h + 6) // 2
            cv2.putText(img, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        cv2.putText(img, f"Thresh: {threshold}", (180, 170), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
    
    def draw_target(self, img):
        cv2.line(img, (Config.TARGET_X - Config.CROSS_SIZE, Config.TARGET_Y), 
                (Config.TARGET_X + Config.CROSS_SIZE, Config.TARGET_Y), (255, 0, 255), 2)
        cv2.line(img, (Config.TARGET_X, Config.TARGET_Y - Config.CROSS_SIZE), 
                (Config.TARGET_X, Config.TARGET_Y + Config.CROSS_SIZE), (255, 0, 255), 2)
        cv2.putText(img, f"({Config.TARGET_X},{Config.TARGET_Y})", 
                   (Config.TARGET_X + 8, Config.TARGET_Y - 8), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

# ========================== 主应用类 ==========================
class JiguangCarApp:
    def __init__(self):
        self.uart = UARTManager()
        self.vision = VisionProcessor()
        self.ui = UIManager()
        self.current_mode = "center"
        self.binary_threshold = Config.BINARY_THRESHOLD
        self.last_touch_pos = (0, 0)
        self.fps = 0
        self.last_time = time.ticks_ms()
        self.frame_count = 0
    
    def init_devices(self):
        gc.disable()
        print("K230激光车程序启动...")
        
        self.disp = display.Display()
        self.cam = camera.Camera(Config.CAMERA_WIDTH, Config.CAMERA_HEIGHT, image.Format.FMT_BGR888)
        
        try:
            self.ts = touchscreen.TouchScreen()
            print("触摸屏初始化成功")
        except Exception as e:
            print(f"触摸屏初始化失败: {e}")
            self.ts = None
        
        if not self.uart.init():
            print("串口初始化失败")
            return False
        
        print("所有设备初始化完成")
        return True
    
    def process_touch(self):
        if not self.ts:
            return
        
        try:
            if self.ts.available():
                touch_data = self.ts.read()
                if len(touch_data) >= 3:
                    touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                    self.last_touch_pos = (touch_x, touch_y)
                    if pressed:
                        action = self.ui.check_touch(touch_x, touch_y)
                        if action:
                            if action == "center":
                                self.current_mode = "center"
                            elif action == "circle":
                                self.current_mode = "circle"
                            elif action == "thresh_up":
                                self.binary_threshold = min(255, self.binary_threshold + 3)
                            elif action == "thresh_down":
                                self.binary_threshold = max(1, self.binary_threshold - 3)
        except Exception as e:
            if self.frame_count % 120 == 0:
                print(f"触摸处理错误: {e}")
    
    def run(self):
        if not self.init_devices():
            return
        
        while not app.need_exit():
            self.frame_count += 1
            
            # 计算FPS
            current_time_ms = time.ticks_ms()
            if current_time_ms - self.last_time > 0:
                self.fps = 1000.0 / (current_time_ms - self.last_time)
            self.last_time = current_time_ms
            
            # 处理触摸
            self.process_touch()
            
            # 读取图像
            img = self.cam.read()
            if img is None:
                continue
            img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
            output = img_cv.copy()
            
            # 矩形检测
            quads = self.vision.detect_rectangles(img_cv, self.binary_threshold)
            center_points = []
            all_circle_points = []
            
            for approx, area in quads:
                pts = approx.reshape(4, 2).astype(np.float32)
                cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)
                
                # 透视变换获取中心点
                M, M_inv, src_pts = self.vision.perspective_transform(pts)
                if M_inv is not None:
                    corrected_center = (Config.CORRECTED_WIDTH//2, Config.CORRECTED_HEIGHT//2)
                    center_np = np.array([[corrected_center]], dtype=np.float32)
                    original_center = cv2.perspectiveTransform(center_np, M_inv)[0][0]
                    cx, cy = int(original_center[0]), int(original_center[1])
                    cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                    center_points.append((cx, cy))
                    
                    # 圆形模式处理
                    if self.current_mode == "circle":
                        corrected_circle = self.vision.generate_circle_points(
                            corrected_center, Config.CIRCLE_RADIUS, Config.CIRCLE_NUM_POINTS
                        )
                        corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                        original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                        original_points = [(int(x), int(y)) for x, y in original_points]
                        all_circle_points.extend(original_points)
                        for (x, y) in original_points:
                            cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
                else:
                    cx = int(np.mean(pts[:, 0]))
                    cy = int(np.mean(pts[:, 1]))
                    cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                    center_points.append((cx, cy))
            
            # 串口发送数据
            if self.current_mode == "center":
                if center_points:
                    cx, cy = center_points[0]
                    self.uart.printf(f"R,{cx},{cy}")
                else:
                    self.uart.printf("R,0,0")
            elif self.current_mode == "circle":
                if all_circle_points:
                    circle_data = f"C,{len(all_circle_points)}"
                    for (x, y) in all_circle_points:
                        circle_data += f",{x},{y}"
                    self.uart.printf(circle_data)
            
            # 绘制界面
            self.ui.draw_target(output)
            self.ui.draw_buttons(output, self.current_mode, self.binary_threshold)
            
            # 显示统计信息
            cv2.putText(output, f"FPS: {self.fps:.1f}", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(output, f"Mode: {self.current_mode.upper()}", (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
            cv2.putText(output, f"Touch: {self.last_touch_pos}", (10, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 0), 1)
            
            # 显示图像
            img_show = image.cv2image(output, bgr=True, copy=False)
            self.disp.show(img_show)

# ========================== 主程序入口 ==========================
if __name__ == "__main__":
    app = JiguangCarApp()
    app.run()
