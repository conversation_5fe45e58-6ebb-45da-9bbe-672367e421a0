# app_config.py - 统一配置管理模块

class AppConfig:
    """激光车应用统一配置类 - 集中管理所有系统参数"""
    
    # ========================== UART通信配置 ==========================
    UART_DEVICE = "/dev/ttyS0"              # UART设备路径
    UART_BAUDRATE = 115200                  # 波特率
    FRAME_HEADER = "$$"                     # 帧头
    FRAME_TAIL = "##"                       # 帧尾
    FRAME_ENABLED = True                    # 启用帧检测
    MAX_BUFFER_SIZE = 1024                  # 缓冲区最大大小
    REFRESH_INTERVAL = 500                  # 刷新间隔(ms)
    
    # ========================== 摄像头配置 ==========================
    CAMERA_WIDTH = 320                      # 摄像头宽度
    CAMERA_HEIGHT = 240                     # 摄像头高度
    CAMERA_FORMAT = "FMT_BGR888"            # 图像格式
    
    # ========================== 视觉处理参数 ==========================
    MIN_CONTOUR_AREA = 500                  # 最小轮廓面积
    MAX_CONTOUR_AREA = 70000                # 最大轮廓面积
    TARGET_SIDES = 4                        # 目标边数(矩形)
    BINARY_THRESHOLD = 66                   # 二值化阈值
    MIN_ASPECT_RATIO = 0.6                  # 最小宽高比
    MAX_ASPECT_RATIO = 1.7                  # 最大宽高比
    EPSILON_FACTOR = 0.03                   # 多边形逼近系数
    
    # ========================== 透视变换参数 ==========================
    CORRECTED_WIDTH = 200                   # 校正后宽度
    CORRECTED_HEIGHT = 150                  # 校正后高度
    CIRCLE_RADIUS = 50                      # 圆形半径
    CIRCLE_NUM_POINTS = 12                  # 圆形点数
    
    # ========================== 触摸控制配置 ==========================
    TOUCH_DEBOUNCE = 0.3                    # 触摸防抖时间(秒)
    
    # 虚拟按键定义 [x, y, width, height, text, action]
    VIRTUAL_BUTTONS = [
        [20, 180, 45, 20, "Center", "center"],
        [115, 180, 50, 20, "Circle", "circle"],
        [180, 180, 25, 20, "T-", "thresh_down"],
        [210, 180, 25, 20, "T+", "thresh_up"]
    ]
    
    # 触摸区域定义 [x, y, width, height]
    TOUCH_AREAS = [
        [70, 265, 100, 40],
        [230, 265, 90, 40],
        [330, 265, 50, 40],
        [390, 265, 50, 40]
    ]
    
    # ========================== 显示配置 ==========================
    TARGET_X = 150                          # 目标点X坐标
    TARGET_Y = 95                           # 目标点Y坐标
    CROSS_SIZE = 5                          # 十字标记大小
    THRESHOLD_STEP = 3                      # 阈值调整步长
    
    # ========================== 激光检测配置 ==========================
    LASER_PIXEL_RADIUS = 3                  # 激光像素半径
    LASER_KERNEL_SIZE = (3, 3)              # 形态学核大小
    
    # ========================== 性能配置 ==========================
    FPS_UPDATE_INTERVAL = 1000              # FPS更新间隔(ms)
    UART_SEND_DELAY = 5                     # UART发送延迟(ms)
    
    @classmethod
    def get_frame_config(cls):
        """获取帧配置字典"""
        return {
            "header": cls.FRAME_HEADER,
            "tail": cls.FRAME_TAIL,
            "enabled": cls.FRAME_ENABLED
        }
    
    @classmethod
    def get_camera_config(cls):
        """获取摄像头配置"""
        return {
            "width": cls.CAMERA_WIDTH,
            "height": cls.CAMERA_HEIGHT,
            "format": cls.CAMERA_FORMAT
        }
    
    @classmethod
    def get_vision_config(cls):
        """获取视觉处理配置"""
        return {
            "min_area": cls.MIN_CONTOUR_AREA,
            "max_area": cls.MAX_CONTOUR_AREA,
            "target_sides": cls.TARGET_SIDES,
            "threshold": cls.BINARY_THRESHOLD,
            "min_ratio": cls.MIN_ASPECT_RATIO,
            "max_ratio": cls.MAX_ASPECT_RATIO,
            "epsilon": cls.EPSILON_FACTOR
        }
    
    @classmethod
    def get_transform_config(cls):
        """获取透视变换配置"""
        return {
            "width": cls.CORRECTED_WIDTH,
            "height": cls.CORRECTED_HEIGHT,
            "radius": cls.CIRCLE_RADIUS,
            "points": cls.CIRCLE_NUM_POINTS
        }
    
    @classmethod
    def get_touch_config(cls):
        """获取触摸控制配置"""
        return {
            "debounce": cls.TOUCH_DEBOUNCE,
            "buttons": cls.VIRTUAL_BUTTONS,
            "areas": cls.TOUCH_AREAS
        }
